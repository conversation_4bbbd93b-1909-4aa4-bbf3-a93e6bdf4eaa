import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { EPlatform } from '../../domain/config/enums/platform.enum';
import { EStatus } from '../../domain/config/enums/status.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { UserEntity } from './user.entity';

@Entity('accounts')
@Index(['userName', 'platform'], { where: 'deleted_at IS NULL' })
export class AccountEntity extends BaseEntity {
  @Column({ type: 'varchar', nullable: true })
  userName: string;

  @Column({ type: 'varchar', nullable: true })
  firstName: string;

  @Column({ type: 'varchar', nullable: true })
  lastName: string;

  @Column({ type: 'varchar', nullable: true })
  email: string;

  @Column({ type: 'varchar', nullable: true })
  staffId: string;

  @Column({ type: 'varchar', nullable: true })
  staffCode: string;

  @Column({
    type: 'varchar',
    enum: EPlatform,
  })
  platform: EPlatform;

  @Column({ type: 'varchar', default: EStatus.ACTIVE, nullable: true })
  status: EStatus;

  @ManyToOne(() => UserEntity, (user) => user.accounts, { eager: true })
  @JoinColumn({ name: 'user_id' })
  @Index()
  user: UserEntity | null;

  @Column({ type: 'boolean', name: 'is_admin', default: false })
  isAdmin: boolean;

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue = (
      removeUnicode(this.firstName) +
      ' ' +
      removeUnicode(this.lastName) +
      ' ' +
      removeUnicode(this.firstName) +
      ' ' +
      removeUnicode(this.lastName) +
      ' ' +
      removeUnicode(this.email)
    )?.trim();
  }
}
