import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { isEmail } from 'class-validator';
import * as Excel from 'exceljs';
import { readFileSync } from 'fs';
import * as _ from 'lodash';
import * as moment from 'moment';
import { resolve } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { CreateAccountDto } from '../controller/account/dtos/create-account.dto';
import { ImportUserDto } from '../controller/import/dtos/import-user.dto';
import { CreateUserMobileDto } from '../controller/user/dtos/create-user-mobile.dto';
import { CreateUserDto } from '../controller/user/dtos/create-user.dto';
import { GetApproverByPositionDto } from '../controller/user/dtos/get-approver-by-position.dto';
import { GetUserListDto } from '../controller/user/dtos/get-user-list.dto';
import { UpdateUserDto } from '../controller/user/dtos/update-user.dto';
import { fileImportPath, regexPhone } from '../domain/config/contants';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import-history.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { EStatus } from '../domain/config/enums/status.enum';
import { EColumnInportUser } from '../domain/config/enums/user.enum';
import { importErrorDetails } from '../domain/messages/error-detail/import';
import { roleErrorDetails } from '../domain/messages/error-detail/role';
import { staffErrorDetails } from '../domain/messages/error-detail/staff';
import { userErrorDetails } from '../domain/messages/error-detail/user';
import { TErrorMessageImport } from '../domain/messages/error-message';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { RoleModel } from '../domain/model/role.model';
import { StaffModel } from '../domain/model/staff.model';
import { UserModel } from '../domain/model/user.model';
import { IUserRepository } from '../domain/repositories/user.repository';
import { generatePassword, getValueOrResult } from '../utils/common';
import { DigiSalesServiceApiUrlsConst } from '../utils/contants/digisales/digisales-service-api-url.constants';
import { IdentityServiceApiUrlsConst } from '../utils/contants/identity-service-api-url.constants';
import { PurchaseServiceApiUrlsConst } from '../utils/contants/purchase-service-api-url.constants';
import { QueueServiceApiUrlsConst } from '../utils/contants/queue-service-api-url.constants';
import { sendPost } from '../utils/http';
import { AccountUsecases } from './account.usecase';
import { EmailUsecases } from './email.usecases';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { FileUsecases } from './file.usecases';
import { RoleUsecases } from './role.usecases';

@Injectable()
export class UserUsecases {
  constructor(
    private roleUsecases: RoleUsecases,
    private emailUsecases: EmailUsecases,
    private accountUsecases: AccountUsecases,
    @Inject(IUserRepository)
    private readonly userRepository: IUserRepository,
    private readonly fileUsecases: FileUsecases,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
  ) {}
  async createUser(data: CreateUserDto, userInfo: any): Promise<UserModel> {
    const [existingUser, existingAccount] = await Promise.all([
      this.userRepository.getUserByEmail(data.email),
      this.accountUsecases.getAccountByUserName(
        data.userName,
        userInfo?.platform,
      ),
    ]);

    if (existingAccount) {
      throw new HttpException(
        userErrorDetails.E_0011(),
        HttpStatus.BAD_REQUEST,
      );
    }

    const isNewUser = !existingUser;

    const user = existingUser || (await this.createNewUser(data, userInfo));

    if (isNewUser) {
      await this.userRepository.createUser(user);
    } else {
      const { password, email, ...rest } = user;
      await this.handleExistingUser(rest, data, userInfo);
    }

    if (isNewUser && !data.password) {
      if (userInfo?.platform !== EPlatform.E_PURCHASE) {
        this.sendNewUserEmail(user, userInfo);
      }
    }

    return await this.getDetailUser(user.id, userInfo?.platform);
  }

  private async createNewUser(
    data: CreateUserDto,
    userInfo: any,
  ): Promise<UserModel> {
    const roles = await this.assignRoleToUser(data.roleIds);
    const accounts = await this.accountUsecases.createMultiAccount(
      data.accounts,
      data,
      userInfo,
    );

    return new UserModel({
      id: uuidv4(),
      // userName: data.userName,
      email: data.email,
      password: data.password || generatePassword(),
      phone: data.phone || '',
      status: data.status,
      isNeedOtp: data.isNeedOtp ?? false,
      isSuperAdmin: data.isSuperAdmin ?? false,
      salt: '',
      roles,
      createdBy: userInfo?.id,
      accounts,
      isMobile: data.isMobile,
    });
  }

  private async handleExistingUser(
    user: UserModel,
    data: CreateUserDto,
    userInfo: any,
  ) {
    await this.validateExistingUser(user, data, userInfo);

    // Tạo mới tài khoản và thêm vào user
    const newAccounts = await this.accountUsecases.createMultiAccount(
      data.accounts,
      data,
      userInfo,
    );
    user.accounts = [...(user.accounts || []), ...newAccounts];

    // Thêm roles mới vào user
    const newRoles = await this.getNewRoles(user.roles, data.roleIds);
    if (newRoles.length) {
      user.roles = [...(user.roles || []), ...newRoles];
    }

    await this.userRepository.updateUser(user);
  }

  private async validateExistingUser(
    user: UserModel,
    data: CreateUserDto,
    userInfo: any,
  ) {
    this.checkForDuplicateAccounts(user);
    await this.checkForExistingAccountWithPlatform(user, userInfo);
    await this.checkForExistingUserNameWithPlatform(data.userName, userInfo);
  }

  private checkForDuplicateAccounts(user: UserModel) {
    const groupBy = _.groupBy(
      user.accounts || [],
      (account) => `${account.userName}-${account.platform}`,
    );
    const invalidKeys = Object.keys(groupBy).filter(
      (key) => groupBy[key].length > 1,
    );

    if (invalidKeys.length > 0) {
      throw new HttpException(
        userErrorDetails.E_0011(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  private async checkForExistingAccountWithPlatform(
    user: UserModel,
    userInfo: any,
  ) {
    const accountWithPlatform = user.accounts?.find(
      (account) =>
        account.platform === (userInfo?.platform || EPlatform.E_PURCHASE),
    );

    if (accountWithPlatform) {
      throw new HttpException(
        userErrorDetails.E_0011(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  private async checkForExistingUserNameWithPlatform(
    userName: string,
    userInfo: any,
  ) {
    const existingUserNameWithPlatform =
      await this.accountUsecases.getAccountByUserName(
        userName,
        userInfo?.platform,
      );

    if (existingUserNameWithPlatform) {
      throw new HttpException(
        userErrorDetails.E_0002(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  private async getNewRoles(
    existingRoles: RoleModel[],
    roleIds: string[],
  ): Promise<RoleModel[]> {
    const existingRoleIds = existingRoles?.map((role) => role.id) || [];
    const newRoleIds =
      roleIds?.filter((roleId) => !existingRoleIds.includes(roleId)) || [];

    return newRoleIds.length ? await this.assignRoleToUser(newRoleIds) : [];
  }

  private sendNewUserEmail(user: UserModel, userInfo: any) {
    try {
      const account = user.accounts.find(
        (account) =>
          account.platform === (userInfo?.platform || EPlatform.E_PURCHASE),
      );

      const titleEmail =
        account?.platform === EPlatform.E_PURCHASE
          ? 'ePurchase: account information'
          : account?.platform === EPlatform.DIGI_SALE
          ? 'DigiSales: Thông tin tài khoản'
          : 'ePurchase: account information';

      const linkTemplate =
        account?.platform === EPlatform.E_PURCHASE
          ? '../domain/email_templates/user-information.ejs'
          : account?.platform === EPlatform.DIGI_SALE
          ? '../domain/email_templates/user-information-digisale.ejs'
          : '../domain/email_templates/user-information.ejs';

      const linkLogin =
        account.platform === EPlatform.E_PURCHASE
          ? process.env.IDENTITY_LOGIN_API_URL
          : account.platform === EPlatform.DIGI_SALE
          ? process.env.IDENTITY_LOGIN_DIGISALE_API_URL
          : process.env.IDENTITY_LOGIN_DIGIAQUA_API_URL;

      this.emailUsecases.sendEmail(
        user.email,
        titleEmail,
        readFileSync(resolve(__dirname, linkTemplate), 'utf8'),
        {
          userName: account?.userName,
          password: user.password,
          linkLogin: linkLogin,
        },
        '',
      );
    } catch (error) {
      console.error('Error sending email:', error);
    }
  }

  async getDetailUser(
    userId: string,
    platform?: EPlatform,
    selectPassword?: boolean,
  ): Promise<UserModel> {
    const user = await this.userRepository.getUserInfo(
      userId,
      platform,
      selectPassword,
    );

    if (!user) {
      throw new HttpException(userErrorDetails.E_0003(), HttpStatus.NOT_FOUND);
    }

    return user;
  }

  async deleteUser(userId: string, userInfo: any): Promise<void> {
    const user = await this.userRepository.getUserInfo(userId);

    if (!user) {
      throw new HttpException(userErrorDetails.E_0003(), HttpStatus.NOT_FOUND);
    }

    await this.accountUsecases.deleteAccountByUserId(userId, userInfo);

    if (user.accounts?.length <= 1) {
      user.deletedBy = userInfo?.id;
      await this.userRepository.updateUser(user);
      await this.userRepository.deleteUser(userId);
    }
  }

  async getUsers(
    conditions: GetUserListDto,
    platform: EPlatform = EPlatform.E_PURCHASE,
  ): Promise<any> {
    return await this.userRepository.getUsers(conditions, platform);
  }

  async updateUser(
    userId: string,
    data: UpdateUserDto,
    userInfo?: any,
  ): Promise<UserModel> {
    let user = await this.userRepository.getUserInfo(
      userId,
      userInfo?.platform,
      false,
      true,
    );

    const { roleIds, accounts, ...rest } = data;

    // user = Object.assign(user, rest);

    user.updatedBy = {
      id: userInfo?.userId,
      firstName: userInfo?.firstName,
      lastName: userInfo?.lastName,
      email: userInfo?.email,
      phone: userInfo?.phone,
      staffId: userInfo?.staffId,
      staffCode: userInfo?.staffCode,
    };

    if (roleIds && roleIds.length) {
      const rolesWithoutPlatform =
        user.roles?.filter((role) => role.platform != userInfo?.platform) || [];

      const updatedRoles = await this.assignRoleToUser(roleIds);

      user.roles = [...rolesWithoutPlatform, ...updatedRoles];
    } else {
      user.roles = undefined;
    }

    if (accounts && accounts.length) {
      await this.accountUsecases.deleteAccountByUserId(userId, userInfo);
      const updatedAccounts = await this.accountUsecases.createMultiAccount(
        accounts,
        rest,
        userInfo,
      );

      user.accounts.push(...updatedAccounts);
    } else {
      user.accounts = undefined;
    }

    if (data.email && data.email !== user.email) {
      const existingUser = await this.userRepository.getUserByEmail(
        data.email,
        userId,
      );

      if (existingUser) {
        throw new HttpException(
          userErrorDetails.E_0001(),
          HttpStatus.NOT_FOUND,
        );
      }

      user.email = data.email;
    }

    user = {
      ...user,
      isNeedOtp:
        user.isNeedOtp != data.isNeedOtp ? data.isNeedOtp : user.isNeedOtp,
      phone: user.phone != data.phone ? data.phone || '' : user.phone || '',
    };
    await this.userRepository.updateUser(user);
    return await this.getDetailUser(userId, userInfo?.platform);
  }

  async assignRoleToUser(roleIds: string[]): Promise<RoleModel[]> {
    const roles: RoleModel[] = [];
    if (roleIds && roleIds.length) {
      for (const roleId of roleIds) {
        const role = await this.roleUsecases.checkRoleById(roleId);
        roles.push(role);
      }
    }
    return roles;
  }

  async getUserByEmail(
    email: string,
    id?: string,
    platform?: EPlatform,
  ): Promise<UserModel> {
    const user = await this.userRepository.getUserByEmail(email, id, platform);

    if (!user) {
      throw new HttpException(
        userErrorDetails.E_0003('USER_NOT_REGISTERED'),
        HttpStatus.NOT_FOUND,
      );
    }

    return user;
  }

  async updateUserPassword(userId: string, password: string): Promise<void> {
    await this.userRepository.updateUserPassword(userId, password);
  }

  async getUserByUserName(
    userName: string,
    platform?: EPlatform,
  ): Promise<UserModel> {
    return await this.userRepository.getUserByUserName(userName, platform);
  }

  async importUser(
    file: Express.Multer.File,
    jwtPayload: any,
    authorization: string,
  ) {
    ///Upload file import
    console.log(319, file);
    const fileImport = await this.fileUsecases.uploadFile(
      file,
      null,
      fileImportPath,
    );
    console.log(817, fileImport);

    if (fileImport) {
      const createFileImportHistory = new FileImportHistoryModel({
        fileName: fileImport.filename,
        filePath: fileImport.path,
        status: EFileImportStatus.WAITING,
        createdBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
        importType: EFileImportType.USER,
      });
      const fileImportHistory =
        await this.fileImportHistoryUsecases.createFileImportHistory(
          createFileImportHistory,
        );

      try {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(Buffer.from(fileImport.buffer) as any);

        const createUserDtos: CreateUserDto[] = [];
        const updateUserDtos: UpdateUserDto[] = [];
        const errors: TErrorMessageImport[] = [];

        const rows =
          workbook.worksheets[0]?.getRows(
            1,
            workbook.worksheets[0].actualRowCount,
          ) ?? [];

        const names = rows
          .map(
            (item) =>
              getValueOrResult(item, EColumnInportUser.NAME)?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        const emails = rows
          .map(
            (item) =>
              getValueOrResult(item, EColumnInportUser.EMAIL)?.toString(),
          )
          ?.slice(1)
          ?.filter(Boolean);

        // Kiểm tra duplicate username trong file
        const nameMap = new Map<string, number[]>();
        names.forEach((name, index) => {
          if (name) {
            if (!nameMap.has(name)) {
              nameMap.set(name, []);
            }
            nameMap.get(name).push(index + 2); // +2 vì slice(1) và row bắt đầu từ 1
          }
        });

        const duplicateUserNames = Array.from(nameMap.entries()).filter(
          ([, rows]) => rows.length > 1,
        );
        if (duplicateUserNames.length > 0) {
          const duplicateNames = duplicateUserNames.map(([name]) => name);
          const duplicateRows = duplicateUserNames.flatMap(([, rows]) => rows);

          errors.push({
            error: userErrorDetails.E_0013(
              `Duplicate usernames in file: ${duplicateNames.join(
                ', ',
              )} at rows: ${duplicateRows.join(', ')}`,
            ),
            row: duplicateRows[0],
          });
        }

        // Kiểm tra duplicate email trong file
        const emailMap = new Map<string, number[]>();
        emails.forEach((email, index) => {
          if (email) {
            if (!emailMap.has(email)) {
              emailMap.set(email, []);
            }
            emailMap.get(email).push(index + 2);
          }
        });

        const duplicateEmails = Array.from(emailMap.entries()).filter(
          ([, rows]) => rows.length > 1,
        );
        if (duplicateEmails.length > 0) {
          const duplicateEmailList = duplicateEmails.map(([email]) => email);
          const duplicateRows = duplicateEmails.flatMap(([, rows]) => rows);

          errors.push({
            error: userErrorDetails.E_0012(
              `Duplicate emails in file: ${duplicateEmailList.join(
                ', ',
              )} at rows: ${duplicateRows.join(', ')}`,
            ),
            row: duplicateRows[0],
          });
        }
        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            importErrorDetails.E_1041({
              totalRow: workbook.worksheets[0].actualRowCount - 1,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const roleCodes = [
          ...new Set(
            _.flatten(
              rows
                .map(
                  (item) =>
                    getValueOrResult(item, EColumnInportUser.ROLE_CODE)
                      ?.toString()
                      ?.split(',')
                      ?.map((item) => item.trim()),
                )
                ?.slice(1)
                ?.filter(Boolean),
            ),
          ),
        ] as string[];

        const staffCodes = [
          ...new Set(
            _.flatten(
              rows
                .map(
                  (item) =>
                    getValueOrResult(item, EColumnInportUser.STAFF_CODE)
                      ?.toString()
                      ?.split(',')
                      ?.map((item) => item.trim()),
                )
                ?.slice(1)
                ?.filter(Boolean),
            ),
          ),
        ] as string[];

        const [userNames, userEmails, roles] = await Promise.all([
          this.userRepository.getUserByUserNames([...new Set(names)]),
          this.userRepository.getUserByEmails([...new Set(emails)]),
          this.roleUsecases.getRoleByCodes(
            roleCodes,
            jwtPayload?.platform || EPlatform.E_PURCHASE,
          ),
        ]);

        let staffs: StaffModel[] = [];

        if (jwtPayload?.platform == EPlatform.E_PURCHASE) {
          const getStaffs = await sendPost(
            PurchaseServiceApiUrlsConst.GET_STAFF_BY_CODES(),
            {
              codes: staffCodes || [],
            },
            { authorization },
          );

          staffs = getStaffs?.data?.data || [];
        } else if (jwtPayload?.platform == EPlatform.DIGI_SALE) {
          const getStaffs = await sendPost(
            DigiSalesServiceApiUrlsConst.GET_STAFF_BY_CODES(),
            {
              codes: staffCodes || [null],
              getAll: 1,
              page: 1,
              limit: 100,
            },
            { authorization },
          );
          // console.log(304, getStaffs);
          staffs = getStaffs?.data?.data.results || [];
        }

        for (let i = 1; i < rows.length; i++) {
          const row = rows[i];

          const name = getValueOrResult(
            row,
            EColumnInportUser.NAME,
          )?.toString();
          const phone = getValueOrResult(
            row,
            EColumnInportUser.PHONE,
          )?.toString();
          const email = getValueOrResult(
            row,
            EColumnInportUser.EMAIL,
          )?.toString();
          const statusValue = getValueOrResult(
            row,
            EColumnInportUser.STATUS,
          )?.toString();
          const roleCodes = [
            ...new Set(
              getValueOrResult(row, EColumnInportUser.ROLE_CODE)
                ?.toString()
                ?.split(',')
                ?.map((item) => item.trim()),
            ),
          ];
          const staffCodes = [
            ...new Set(
              getValueOrResult(row, EColumnInportUser.STAFF_CODE)
                ?.toString()
                ?.split(',')
                ?.map((item) => item.trim()),
            ),
          ];

          // Xác định status dựa trên cột F
          const status = statusValue == 'x' ? EStatus.ACTIVE : EStatus.INACTIVE;

          // Tìm staff để lấy thông tin mặc định
          const staffInfo = staffs.find((staff) =>
            staffCodes.includes(staff.code),
          );

          // Xác định phone và email cuối cùng
          const finalPhone = phone || staffInfo?.phone || '';
          const finalEmail = email || staffInfo?.email || '';

          const userDto: CreateUserDto | UpdateUserDto = {
            id: undefined,
            userName: name,
            phone: finalPhone,
            email: finalEmail,
            roleIds: null,
            status: status,
            createdAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            updatedAt: moment()
              .add(i * 500, 'milliseconds')
              .toISOString(),
            isSuperAdmin: false,
            isNeedOtp: false,
          };

          let existingUser: UserModel;

          // Kiểm tra Name
          if (!name) {
            errors.push({
              error: userErrorDetails.E_0004(),
              row: i + 1,
            });
          }

          // Kiểm tra Email (chỉ validate format nếu có email)
          if (!finalEmail) {
            errors.push({
              error: userErrorDetails.E_0005(),
              row: i + 1,
            });
          } else if (!isEmail(finalEmail)) {
            errors.push({
              error: userErrorDetails.E_0008(),
              row: i + 1,
            });
          } else {
            const existing = userEmails.find(
              (item) => item.email === finalEmail,
            );
            if (existing) {
              // Kiểm tra nếu email đã tồn tại với userName khác
              const existingUserWithDifferentUserName = userNames.find(
                (account) =>
                  account.userName !== name &&
                  account.user &&
                  account.user.email === finalEmail,
              );

              if (existingUserWithDifferentUserName) {
                errors.push({
                  error: userErrorDetails.E_0001(), // Email is duplicated
                  row: i + 1,
                });
              } else {
                existingUser = existing;
                userDto.id = existingUser.id;
              }
            }
          }

          // Kiểm tra Phone (chỉ validate format nếu có phone)
          if (finalPhone && !regexPhone.test(finalPhone)) {
            errors.push({
              error: userErrorDetails.E_0006(),
              row: i + 1,
            });
          }

          ///Kiểm tra role
          if (roleCodes && roleCodes.length) {
            const rolesFilter = roles.filter((item) =>
              roleCodes.includes(item.code),
            );

            const checkRoleCodes = (rolesFilter ?? []).map((item) => item.code);

            const diffRoleCodes = _.difference(roleCodes, checkRoleCodes);

            if (diffRoleCodes?.length) {
              errors.push({
                error: roleErrorDetails.E_1001(),
                row: i + 1,
              });
            }

            const rolesInactive = rolesFilter.filter(
              (item) => item.status == EStatus.INACTIVE,
            );

            if (rolesInactive.length) {
              errors.push({
                error: roleErrorDetails.E_1003(),
                row: i + 1,
              });
            }

            userDto.roleIds = [
              ...new Set([
                ...(existingUser?.roles || []).map((item) => item.id),
                ...rolesFilter.map((item) => item.id),
              ]),
            ];
          }

          if (staffCodes && staffCodes.length) {
            const staffsFilter = staffs.filter((item) =>
              staffCodes.includes(item.code),
            );

            const checkStaffCodes = (staffsFilter ?? []).map(
              (item) => item.code,
            );

            const diffStaffCodes = _.difference(staffCodes, checkStaffCodes);

            if (diffStaffCodes?.length) {
              errors.push({
                error: staffErrorDetails.E_3000(),
                row: i + 1,
              });
            }

            const staffsInactive = staffsFilter.filter(
              (item) => item.status == EStatus.INACTIVE,
            );

            if (staffsInactive.length) {
              errors.push({
                error: staffErrorDetails.E_3005(),
                row: i + 1,
              });
            }

            const accounts: CreateAccountDto[] = [];

            for (const staff of staffsFilter) {
              const accountInfo: CreateAccountDto = {
                userName: name,
                firstName: staff.firstName || staff.fullName || '',
                lastName: staff.lastName || '',
                email: finalEmail, // Sử dụng finalEmail thay vì staff.email
                staffId: staff.id,
                staffCode: staff.code,
                platform: jwtPayload?.platform || EPlatform.E_PURCHASE,
                status: status, // Sử dụng status từ cột F
                isAdmin: false,
              };

              accounts.push(accountInfo);
            }

            userDto.accounts = [...(existingUser?.accounts || []), ...accounts];
          }

          if (existingUser) {
            ///Check For Existing User Name With Platform
            const existingUserNameWithPlatform = userNames?.find(
              (account) =>
                account.platform ===
                  (jwtPayload?.platform || EPlatform.E_PURCHASE) &&
                account.userName == name,
            );
            console.log(751, existingUserNameWithPlatform);
            if (
              existingUserNameWithPlatform &&
              existingUserNameWithPlatform.user.id != existingUser.id
            ) {
              errors.push({
                error: userErrorDetails.E_0002(),
                row: i + 1,
              });
            }

            ///Check For Existing Account With Platform
            // const accountWithPlatform = existingUser?.accounts?.find(
            //   (account) =>
            //     account.platform ===
            //     (jwtPayload?.platform || EPlatform.E_PURCHASE),
            // );

            // if (accountWithPlatform) {
            //   errors.push({
            //     error: userErrorDetails.E_0011(),
            //     row: i + 1,
            //   });
            // }

            ///Check For Duplicate Accounts
            const groupBy = _.groupBy(
              [...(existingUser?.accounts || []), ...(userDto.accounts || [])],
              (account) => `${account.userName}-${account.platform}`,
            );
            const invalidKeys = Object.keys(groupBy).filter(
              (key) => groupBy[key].length > 1,
            );

            if (invalidKeys.length > 0) {
              errors.push({
                error: userErrorDetails.E_0011(),
                row: i + 1,
              });
            }
          }

          if (existingUser) {
            // Soft delete accounts cũ của platform hiện tại
            const accountsToDelete =
              existingUser.accounts?.filter(
                (account) =>
                  account.platform ===
                  (jwtPayload?.platform || EPlatform.E_PURCHASE),
              ) || [];

            for (const account of accountsToDelete) {
              account.deletedBy = {
                id: jwtPayload?.userId,
                firstName: jwtPayload?.firstName,
                lastName: jwtPayload?.lastName,
                email: jwtPayload?.email,
                phone: jwtPayload?.phone,
                staffId: jwtPayload?.staffId,
                staffCode: jwtPayload?.staffCode,
              };
              account.deletedAt = new Date();
            }

            // Lọc bỏ accounts cũ của platform hiện tại
            const existingAccountsOtherPlatform =
              existingUser.accounts?.filter(
                (account) =>
                  account.platform !==
                  (jwtPayload?.platform || EPlatform.E_PURCHASE),
              ) || [];

            // Merge với accounts mới
            userDto.accounts = [
              ...existingAccountsOtherPlatform,
              ...(userDto.accounts || []),
            ];

            updateUserDtos.push(userDto as UpdateUserDto);
          } else {
            createUserDtos.push(userDto as CreateUserDto);
          }
        }

        if (errors.length) {
          const updateFileImportHistory = new FileImportHistoryModel({
            errors: errors,
            status: EFileImportStatus.FAIL,
          });
          await this.fileImportHistoryUsecases.updateFileImportHistory(
            fileImportHistory.id,
            updateFileImportHistory,
          );

          throw new HttpException(
            importErrorDetails.E_1041({
              totalRow: workbook.worksheets[0].actualRowCount - 1,
              totalRowError: [...new Set(errors.map((item) => item.row))]
                .length,
              errorDetail: errors,
            }),
            HttpStatus.BAD_REQUEST,
          );
        }

        const importBody: ImportUserDto = {
          dataUsers: createUserDtos,
          dataUpdateUsers: updateUserDtos,
          fileImportHistoryId: fileImportHistory.id,
          userInfo: jwtPayload,
        };
        await sendPost(QueueServiceApiUrlsConst.IMPORT_QUEUE(), {
          importBody: importBody,
          importUrl: IdentityServiceApiUrlsConst.IMPORT_USER(),
          importHeader: {
            authorization,
            'x-api-key': process.env.API_KEY,
          },
          updateStatusFileUrl:
            IdentityServiceApiUrlsConst.UPDATE_STATUS_FILE_IMPORT(
              fileImportHistory.id,
            ),
        });

        return { fileImportHistoryId: fileImportHistory.id };
      } catch (error) {
        const updateFileImportHistory = new FileImportHistoryModel({
          errors: error,
          status: EFileImportStatus.FAIL,
        });
        await this.fileImportHistoryUsecases.updateFileImportHistory(
          fileImportHistory.id,
          updateFileImportHistory,
        );
        throw error;
      }
    } else {
      throw new HttpException(
        importErrorDetails.E_5000(),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async approverByPosition(conditions: GetApproverByPositionDto) {
    return await this.userRepository.approverByPosition(conditions);
  }

  ///For Mobile
  async deleteUserMobile(userId: string, userInfo: any): Promise<void> {
    const user = await this.userRepository.getUserInfoMobile(userId);

    if (!user) {
      throw new HttpException(userErrorDetails.E_0003(), HttpStatus.NOT_FOUND);
    }

    await this.accountUsecases.deleteAccountByUserId(userId, userInfo);

    if (user.accounts?.length <= 1) {
      user.deletedBy = userInfo?.id;
      await this.userRepository.updateUser(user);
      await this.userRepository.deleteUser(userId);
    }
  }

  async createUserMobile(
    data: CreateUserMobileDto,
    userInfo: any,
  ): Promise<UserModel> {
    data.phone = data.phone || '';

    const [existingUser, existingAccount] = await Promise.all([
      this.userRepository.getUserByEmail(data.email),
      this.accountUsecases.getAccountByUserName(data.userName, data.platform),
    ]);

    if (existingAccount) {
      throw new HttpException(
        userErrorDetails.E_0011(),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (existingUser) {
      throw new HttpException(
        userErrorDetails.E_0001(),
        HttpStatus.BAD_REQUEST,
      );
    }

    const user = await this.createNewUserMobile(data, userInfo);

    await this.userRepository.createUser(user);

    return await this.getDetailUser(user.id, data.platform);
  }

  private async createNewUserMobile(
    data: CreateUserMobileDto,
    userInfo: any,
  ): Promise<UserModel> {
    const roleAllData = await this.roleUsecases.getRoleAllData();
    const accounts = await this.accountUsecases.createMultiAccountMobile(
      data.accounts,
      data,
      userInfo,
    );

    return new UserModel({
      id: uuidv4(),
      email: data.email,
      password: data.password,
      phone: data.phone,
      status: data.status,
      isNeedOtp: data.isNeedOtp ?? false,
      isSuperAdmin: data.isSuperAdmin ?? false,
      salt: '',
      roles: [roleAllData],
      createdBy: 'MOBILE USER',
      accounts,
      isMobile: data.isMobile,
    });
  }
}
